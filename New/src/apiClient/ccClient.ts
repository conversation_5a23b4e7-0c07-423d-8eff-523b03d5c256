/**
 * ClinicalCare (CC) API Client
 *
 * Comprehensive API client for ClinicalCare system providing all CRUD operations
 * for patients, appointments, custom fields, users, services, resources, locations,
 * invoices, and payments.
 *
 * Features:
 * - Complete feature parity with v3Integration
 * - TypeScript typing and error handling
 * - Configuration from configs.ts
 * - Data cleaning utilities
 */

import getConfig from "@config";
import type {
	GetCCAppointmentCategoryType,
	GetCCAppointmentType,
	GetCCCustomField,
	GetCCLocationType,
	GetCCPatientCustomField,
	GetCCPatientType,
	GetCCResourceType,
	GetCCServiceType,
	GetCCUserType,
	GetInvoiceType,
	GetPaymentType,
	PostCCAppointmentType,
	PostCCCustomField,
	PostCCPatientType,
	PutCCAppointmentType,
} from "@type/CCTypes";
import { apiResponseCache } from "@utils/advancedCache";
import cleanData from "@utils/cleanData";
import { logDebug } from "@/utils/logger";

/**
 * HTTP request options interface
 */
interface RequestOptions {
	url: string;
	method: "GET" | "POST" | "PUT" | "DELETE";
	data?: unknown;
	params?: Record<string, string | number | boolean>;
	invalidateCache?: boolean;
}

/**
 * Base HTTP client for ClinicalCare API with intelligent caching
 */
const ccRequest = async <
	T extends Record<string, unknown> = Record<string, unknown>,
>(
	options: RequestOptions,
): Promise<T> => {
	const { url, method, data, params, invalidateCache = false } = options;

	// Build full URL with base domain
	const baseUrl = getConfig("ccApiDomain");
	let fullUrl = `${baseUrl}${url}`;

	// Add query parameters if provided
	if (params) {
		const searchParams = new URLSearchParams();
		Object.entries(params).forEach(([key, value]) => {
			searchParams.append(key, String(value));
		});
		fullUrl += `?${searchParams.toString()}`;
	}

	// Generate cache key for GET requests
	const cacheKey = apiResponseCache.generateKey(method, url, params);

	// Check cache for GET requests (unless cache invalidation is requested)
	if (method === "GET" && !invalidateCache) {
		const cachedData = apiResponseCache.get(cacheKey);
		if (cachedData) {
			return cachedData as T;
		}
	}

	// Prepare request headers
	const headers: Record<string, string> = {
		Authorization: `Bearer ${getConfig("ccApiKey")}`,
		Accept: "application/json",
		"Content-Type": "application/json",
	};

	try {
		const response = await fetch(fullUrl, {
			method,
			headers,
			body: data ? JSON.stringify(data) : undefined,
		});

		if (!response.ok) {
			const errorText = await response.text();
			let errorMessage: string;
			let errorDetails: Record<string, unknown> = {};

			try {
				const errorData = JSON.parse(errorText);
				errorDetails = errorData;

				// Extract meaningful error message from various possible structures
				errorMessage =
					errorData.message ||
					errorData.error ||
					errorData.errors?.[0]?.message ||
					errorData.detail ||
					errorData.title ||
					`HTTP ${response.status}: ${response.statusText}`;

				// If errorData has validation errors, include them
				if (errorData.errors && Array.isArray(errorData.errors)) {
					const validationErrors = errorData.errors
						.map((err: any) =>
							typeof err === "string"
								? err
								: err.message || err.detail || JSON.stringify(err),
						)
						.join("; ");
					errorMessage += ` | Validation errors: ${validationErrors}`;
				}
			} catch {
				errorMessage = `HTTP ${response.status}: ${response.statusText}`;
				errorDetails = { rawResponse: errorText };
			}

			// Enhanced error with detailed information
			const enhancedError = new Error(`CC API Error: ${errorMessage}`);
			(enhancedError as any).details = {
				status: response.status,
				statusText: response.statusText,
				url: fullUrl,
				method,
				requestData: data,
				responseData: errorDetails,
				timestamp: new Date().toISOString(),
			};

			throw enhancedError;
		}

		const responseData = await response.json();

		// Cache successful GET responses
		if (method === "GET") {
			apiResponseCache.set(cacheKey, responseData);
		}

		// Invalidate related cache entries for mutating operations
		if (method === "POST" || method === "PUT" || method === "DELETE") {
			const invalidatedCount = apiResponseCache.invalidatePattern(url);
			if (invalidatedCount > 0) {
				logDebug(
					"api",
					`CC API: Invalidated ${invalidatedCount} cache entries for ${method} ${url}`,
				);
			}
		}

		return responseData as T;
	} catch (error) {
		if (error instanceof Error) {
			// If this is our enhanced error with details, preserve them
			if ((error as any).details) {
				const enhancedError = new Error(
					`CC API Request Failed: ${error.message}`,
				);
				(enhancedError as any).details = (error as any).details;
				throw enhancedError;
			}
			throw new Error(`CC API Request Failed: ${error.message}`);
		}
		throw new Error("CC API Request Failed: Unknown error");
	}
};

/**
 * Convert array of IDs to query string format
 */
const idsToQueryString = (ids: number[]): string => {
	return ids
		.map((id) => `ids[]=${id.toString().trim()}`)
		.join("&")
		.trim();
};

/**
 * Patient operations
 */
export const patientReq = {
	/**
	 * Create a new patient
	 */
	create: async (data: PostCCPatientType): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: "/patients",
			method: "POST",
			data: {
				patient: cleanData(data),
			},
		});
		return response.patient;
	},

	/**
	 * Update an existing patient
	 */
	update: async (
		id: number,
		data: PostCCPatientType,
	): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: `/patients/${id}`,
			method: "PUT",
			data: {
				patient: cleanData(data),
			},
		});
		return response.patient;
	},

	/**
	 * Get a patient by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCPatientType> => {
		const response = await ccRequest<{ patient: GetCCPatientType }>({
			url: `/patients/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.patient;
	},

	/**
	 * Search for a patient by email or phone
	 */
	search: async (
		emailOrPhone: string,
		invalidateCache?: boolean,
	): Promise<GetCCPatientType | null> => {
		const response = await ccRequest<{ patients: GetCCPatientType[] }>({
			url: `/patients`,
			method: "GET",
			params: { search: emailOrPhone },
			invalidateCache,
		});
		return response.patients && response.patients.length > 0
			? response.patients[0]
			: null;
	},

	/**
	 * Get all patients with pagination and filtering
	 */
	all: async (
		params: {
			page?: number;
			perPage?: number;
			active?: boolean;
			sort?: string;
			invalidateCache?: boolean;
		} = {
			page: 1,
			perPage: 20,
			active: true,
			sort: "-createdAt",
		},
	): Promise<GetCCPatientType[]> => {
		const { invalidateCache, ...queryParams } = params;
		const requestParams = {
			[`active${queryParams.active}`]: "",
			"page[number]": queryParams.page || 1,
			"page[size]": queryParams.perPage || 20,
			sort: queryParams.sort || "-createdAt",
		};

		const response = await ccRequest<{ patients: GetCCPatientType[] }>({
			url: "/patients",
			method: "GET",
			params: requestParams,
			invalidateCache,
		});
		return response.patients;
	},

	/**
	 * Get patient custom fields by IDs
	 */
	customFields: async (
		ids: number[],
		invalidateCache?: boolean,
	): Promise<GetCCPatientCustomField[]> => {
		const response = await ccRequest<{
			patientCustomFields: GetCCPatientCustomField[];
		}>({
			url: `/patientCustomFields?${idsToQueryString(ids)}`,
			method: "GET",
			invalidateCache,
		});
		return response.patientCustomFields;
	},
};

/**
 * Custom field operations
 */
export const ccCustomfieldReq = {
	/**
	 * Get a custom field by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCCustomField> => {
		const response = await ccRequest<{ customField: GetCCCustomField }>({
			url: `/customFields/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.customField;
	},

	/**
	 * Get all custom fields
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCCustomField[]> => {
		const response = await ccRequest<{ customFields: GetCCCustomField[] }>({
			url: "/customFields/",
			method: "GET",
			invalidateCache,
		});
		return response.customFields;
	},

	/**
	 * Create a new custom field
	 */
	create: async (data: PostCCCustomField): Promise<GetCCCustomField> => {
		const response = await ccRequest<{ customField: GetCCCustomField }>({
			url: "/customFields",
			method: "POST",
			data: {
				customField: cleanData(data),
			},
		});
		return response.customField;
	},
};

/**
 * User operations
 */
export const ccUserReq = {
	/**
	 * Get a user by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCUserType> => {
		const response = await ccRequest<{ user: GetCCUserType }>({
			url: `/users/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.user;
	},

	/**
	 * Get all users
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCUserType[]> => {
		const response = await ccRequest<{ users: GetCCUserType[] }>({
			url: "/users",
			method: "GET",
			invalidateCache,
		});
		return response.users;
	},
};

/**
 * Service operations
 */
export const serviceReq = {
	/**
	 * Get all services
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCServiceType[]> => {
		const response = await ccRequest<{ services: GetCCServiceType[] }>({
			url: "/services",
			method: "GET",
			invalidateCache,
		});
		return response.services;
	},

	/**
	 * Get a service by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCServiceType> => {
		const response = await ccRequest<{ service: GetCCServiceType }>({
			url: `/services/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.service;
	},
};

/**
 * Resource operations
 */
export const resourceReq = {
	/**
	 * Get all resources
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCResourceType[]> => {
		const response = await ccRequest<{ resources: GetCCResourceType[] }>({
			url: "/resources",
			method: "GET",
			invalidateCache,
		});
		return response.resources;
	},

	/**
	 * Get a resource by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCResourceType> => {
		const response = await ccRequest<{ resource: GetCCResourceType }>({
			url: `/resources/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.resource;
	},
};

/**
 * Location operations
 */
export const ccLocationReq = {
	/**
	 * Get all locations
	 */
	all: async (invalidateCache?: boolean): Promise<GetCCLocationType[]> => {
		const response = await ccRequest<{ locations: GetCCLocationType[] }>({
			url: "/locations",
			method: "GET",
			invalidateCache,
		});
		return response.locations;
	},

	/**
	 * Get a location by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCLocationType> => {
		const response = await ccRequest<{ location: GetCCLocationType }>({
			url: `/locations/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.location;
	},
};

/**
 * Appointment operations
 */
export const ccAppointmentReq = {
	/**
	 * Get an appointment by ID
	 */
	get: async (
		id: number,
		invalidateCache?: boolean,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: `/appointments/${id}`,
			method: "GET",
			invalidateCache,
		});
		return response.appointment;
	},

	/**
	 * Create a new appointment
	 */
	post: async (
		payload: PostCCAppointmentType,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: "/appointments",
			method: "POST",
			data: { appointment: payload },
		});
		return response.appointment;
	},

	/**
	 * Update an existing appointment
	 */
	put: async (
		id: number,
		payload: PutCCAppointmentType,
	): Promise<GetCCAppointmentType> => {
		const response = await ccRequest<{ appointment: GetCCAppointmentType }>({
			url: `/appointments/${id}`,
			method: "PUT",
			data: { appointment: payload },
		});
		return response.appointment;
	},

	/**
	 * Appointment category operations
	 */
	category: {
		/**
		 * Get an appointment category by ID
		 */
		get: async (
			id: number,
			invalidateCache?: boolean,
		): Promise<GetCCAppointmentCategoryType> => {
			const response = await ccRequest<{
				appointmentCategory: GetCCAppointmentCategoryType;
			}>({
				url: `/appointmentCategories/${id}`,
				method: "GET",
				invalidateCache,
			});
			return response.appointmentCategory;
		},

		/**
		 * Get all appointment categories
		 */
		all: async (
			invalidateCache?: boolean,
		): Promise<GetCCAppointmentCategoryType[]> => {
			const response = await ccRequest<{
				appointmentCategories: GetCCAppointmentCategoryType[];
			}>({
				url: "/appointmentCategories",
				method: "GET",
				invalidateCache,
			});
			return response.appointmentCategories;
		},
	},
};

/**
 * Invoice operations
 */
export const invoiceReq = {
	/**
	 * Get invoices by IDs
	 */
	get: async (
		ids: number[],
		invalidateCache?: boolean,
	): Promise<GetInvoiceType[]> => {
		const response = await ccRequest<{ invoices: GetInvoiceType[] }>({
			url: `/invoices?${idsToQueryString(ids)}`,
			method: "GET",
			invalidateCache,
		});
		return response.invoices;
	},
};

/**
 * Payment operations
 */
export const paymentReq = {
	/**
	 * Get payments by IDs
	 */
	get: async (
		ids: number[],
		invalidateCache?: boolean,
	): Promise<GetPaymentType[]> => {
		const response = await ccRequest<{ payments: GetPaymentType[] }>({
			url: `/payments?${idsToQueryString(ids)}`,
			method: "GET",
			invalidateCache,
		});
		return response.payments;
	},
};
