/**
 * AP to CC Custom Field Synchronization
 *
 * Simple, robust synchronization of custom fields from AutoPatient (AP) to CliniCore (CC)
 * based on v3Integration patterns with TypeScript compliance.
 *
 * Key Features:
 * - Simple field name matching (name or label)
 * - Minimal value transformation (pass-through approach)
 * - Automatic creation of missing CC fields as TEXT type
 * - Graceful error handling that continues sync even if some fields fail
 * - Direct field name matching without complex type compatibility checks
 *
 * This replaces the complex type conversion logic with v3Integration's proven approach.
 */

import type {
	APGetCustomFieldType,
	GetAPContactType,
	GetCCCustomField,
	PostCCPatientCustomfield,
} from "@type";
import { apCustomfield, ccCustomfieldReq, patientReq } from "@/apiClient";
import { fieldNamesMatch } from "@/processors/cc/fieldMatcher";
import { logDebug, logError, logInfo, logWarn } from "@/utils/logger";
import {
	createCcCustomField,
	findExistingCcField,
} from "./ccCustomFieldCreator";

/**
 * Type for AP custom field name-value mapping
 */
interface ApCustomFieldMap {
	[fieldName: string]: string;
}

/**
 * Synchronize AP custom fields to CC patient
 * Based on v3Integration's syncApToCcCustomfields function with TypeScript compliance
 *
 * @param apContactData - AP contact data containing custom fields
 * @param ccPatientId - CC patient ID to update
 * @param requestId - Request ID for logging
 * @returns Promise<void>
 */
export async function syncApToCcCustomFields(
	apContactData: GetAPContactType,
	ccPatientId: number,
	requestId: string,
): Promise<void> {
	logDebug(
		requestId,
		`Starting AP to CC custom field sync for CC patient ${ccPatientId}`,
	);

	try {
		// Step 1: Get AP custom field definitions
		const apCustomFields = await apCustomfield.all();
		logDebug(
			requestId,
			`Fetched ${apCustomFields.length} AP custom field definitions`,
		);

		// Step 2: Create AP field name-value map (v3Integration pattern)
		const apFieldMap = createApFieldMap(
			apContactData,
			apCustomFields,
			requestId,
		);

		// Add standard contact fields to the map (v3Integration pattern)
		if (apContactData.email) {
			apFieldMap["email"] = apContactData.email;
		}
		if (apContactData.phone) {
			apFieldMap["phoneMobile"] = apContactData.phone;
			apFieldMap["phone-mobile"] = apContactData.phone;
			apFieldMap["phone"] = apContactData.phone;
		}

		if (Object.keys(apFieldMap).length === 0) {
			logDebug(requestId, "No AP custom field values to sync");
			return;
		}

		logInfo(
			requestId,
			`Created AP field map with ${Object.keys(apFieldMap).length} fields`,
		);

		// Step 3: Get CC custom field definitions
		const ccCustomFields = await ccCustomfieldReq.all();
		logDebug(
			requestId,
			`Fetched ${ccCustomFields.length} CC custom field definitions`,
		);

		// Step 4: Match fields and create missing ones (enhanced v3Integration pattern)
		const matchedFields = await matchAndCreateFields(
			apFieldMap,
			ccCustomFields,
			requestId,
		);

		if (matchedFields.length === 0) {
			logInfo(requestId, "No custom field mappings created");
			return;
		}

		// Step 5: Update CC patient with custom fields
		logInfo(
			requestId,
			`Updating CC patient with ${matchedFields.length} custom fields`,
		);

		// Use type assertion to bypass the PostCCPatientType constraint
		// This follows the v3Integration pattern where custom fields are sent as objects, not IDs
		await patientReq.update(ccPatientId, {
			customFields: matchedFields,
		} as any);

		logInfo(
			requestId,
			`Successfully synced ${matchedFields.length} custom fields to CC patient ${ccPatientId}`,
		);
	} catch (error) {
		logError(requestId, "Failed to sync AP to CC custom fields:", error);
		throw error;
	}
}

/**
 * Create AP field name-value map from contact data
 * Based on v3Integration pattern
 *
 * @param apContactData - AP contact data
 * @param apCustomFields - AP custom field definitions
 * @param requestId - Request ID for logging
 * @returns Field name to value mapping
 */
function createApFieldMap(
	apContactData: GetAPContactType,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): ApCustomFieldMap {
	const fieldMap: ApCustomFieldMap = {};

	if (!apContactData.customFields || apContactData.customFields.length === 0) {
		logDebug(requestId, "No custom fields in AP contact data");
		return fieldMap;
	}

	// Map custom field values to field names (v3Integration pattern)
	for (const customField of apContactData.customFields) {
		const fieldDefinition = apCustomFields.find((f) => f.id === customField.id);

		if (fieldDefinition && customField.value) {
			// Use field name as key, value as value (simple pass-through)
			fieldMap[fieldDefinition.name] = String(customField.value).trim();

			logDebug(
				requestId,
				`Mapped AP field "${fieldDefinition.name}" = "${String(customField.value).substring(0, 50)}${String(customField.value).length > 50 ? "..." : ""}"`,
			);
		}
	}

	return fieldMap;
}

/**
 * Match AP fields to CC fields and create missing ones
 * Enhanced v3Integration pattern with field creation capability
 *
 * @param apFieldMap - AP field name-value mapping
 * @param ccCustomFields - Existing CC custom fields
 * @param requestId - Request ID for logging
 * @returns Array of matched CC custom field mappings
 */
async function matchAndCreateFields(
	apFieldMap: ApCustomFieldMap,
	ccCustomFields: GetCCCustomField[],
	requestId: string,
): Promise<PostCCPatientCustomfield[]> {
	const matchedFields: PostCCPatientCustomfield[] = [];
	let matchedCount = 0;
	let createdCount = 0;
	let skippedCount = 0;

	for (const [fieldName, fieldValue] of Object.entries(apFieldMap)) {
		try {
			// Try to find existing CC field (v3Integration pattern: name or label match)
			let ccField = findExistingCcField(fieldName, ccCustomFields, requestId);

			// Create CC field if it doesn't exist (enhancement over v3Integration)
			if (!ccField) {
				try {
					ccField = await createCcCustomField(fieldName, requestId);
					// Add to local cache to avoid duplicate creation
					ccCustomFields.push(ccField);
					createdCount++;
				} catch (createError) {
					logError(
						requestId,
						`Failed to create CC custom field for "${fieldName}":`,
						createError,
					);
					skippedCount++;
					continue; // Skip this field and continue with others
				}
			} else {
				matchedCount++;
			}

			// Create field mapping (v3Integration pattern)
			const fieldMapping: PostCCPatientCustomfield = {
				field: ccField,
				values: [{ value: fieldValue }],
				patient: null, // Will be set by CC API
			};

			// Handle allowedValues if present (v3Integration pattern)
			if (ccField.allowedValues && ccField.allowedValues.length > 0) {
				const allowedValue = ccField.allowedValues.find(
					(v) => v.value === fieldValue,
				);
				if (allowedValue) {
					fieldMapping.values = [{ id: allowedValue.id }];
					logDebug(
						requestId,
						`Using allowed value ID ${allowedValue.id} for field "${fieldName}"`,
					);
				}
			}

			matchedFields.push(fieldMapping);

			logDebug(
				requestId,
				`Mapped field "${fieldName}" to CC field "${ccField.label}" (ID: ${ccField.id})`,
			);
		} catch (error) {
			logError(requestId, `Error processing field "${fieldName}":`, error);
			skippedCount++;
			// Continue with other fields (v3Integration robustness)
		}
	}

	logInfo(
		requestId,
		`Field matching summary: ${matchedCount} matched, ${createdCount} created, ${skippedCount} skipped`,
	);

	return matchedFields;
}
