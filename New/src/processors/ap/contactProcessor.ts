/**
 * Contact Processor for AP Webhook Events
 *
 * Handles contact creation and update events from AutoPatient webhooks by:
 * 1. Checking for calendar property (skip if present)
 * 2. Looking up existing contact in local database
 * 3. Fetching complete contact details from AutoPatient API
 * 4. Comparing timestamps for recent updates
 * 5. Syncing with CliniCore platform
 * 6. Updating local database with results
 *
 * Performance Critical: Must complete within 20 seconds (Cloudflare Workers timeout)
 */

import { dbSchema, getDb } from "@database";
import type {
	APContactWebhookPayload,
	APGetCustomFieldType,
	APWebhookPayload,
	GetAPContactType,
	GetCCCustomField,
	GetCCPatientType,
	PostCCPatientCustomfield,
	PostCCPatientType,
} from "@type";
import { eq, or } from "drizzle-orm";
import {
	apCustomfield,
	ccCustomfieldReq,
	contactReq,
	patientReq,
} from "@/apiClient";
import { fieldNamesMatch } from "@/processors/cc/fieldMatcher";
import cleanData from "@/utils/cleanData";
import { getConfig } from "@/utils/configs";
import {
	logDebug,
	logError,
	logInfo,
	logProcessingStep,
	logWarn,
} from "@/utils/logger";
import {
	areFieldTypesCompatible,
	logTypeConversion,
	transformApToCcValue,
} from "./typeConverter";

/**
 * Normalize contact field values (email, phone) following v3Integration patterns
 * Converts empty strings and whitespace-only strings to null
 *
 * @param value - Raw field value
 * @returns Normalized value or null
 */
function normalizeContactField(
	value: string | undefined | null,
): string | null {
	if (!value || typeof value !== "string") return null;
	const trimmed = value.trim();
	return trimmed === "" ? null : trimmed;
}

/**
 * Format date value for CliniCore API compatibility
 * Converts various date formats to ISO 8601 format required by CC API
 *
 * The CliniCore API (based on HighLevel) expects dates in ISO 8601 format with time component:
 * "2000-01-05T00:00:00.000Z" instead of simple date strings like "1994-04-04"
 *
 * @param dateValue - Raw date value (YYYY-MM-DD, MM/DD/YYYY, or ISO format)
 * @param requestId - Request ID for logging (optional)
 * @returns ISO 8601 formatted date string or null if invalid
 */
function formatDateForCC(
	dateValue: string | null | undefined,
	requestId?: string,
): string | null {
	if (!dateValue || typeof dateValue !== "string") return null;

	const trimmed = dateValue.trim();
	if (trimmed === "") return null;

	try {
		// If already in ISO format, validate and return
		if (trimmed.includes("T") && trimmed.includes("Z")) {
			const date = new Date(trimmed);
			if (!isNaN(date.getTime())) {
				if (requestId) {
					logDebug(requestId, `Date already in ISO format: "${trimmed}"`);
				}
				return trimmed; // Already valid ISO format
			}
		}

		// Handle common date formats (YYYY-MM-DD, MM/DD/YYYY, etc.)
		const date = new Date(trimmed);
		if (isNaN(date.getTime())) {
			if (requestId) {
				logWarn(requestId, `Invalid date format for CC API: "${trimmed}"`);
			}
			return null;
		}

		// Convert to ISO 8601 format with UTC timezone
		const isoDate = date.toISOString();
		if (requestId) {
			logDebug(
				requestId,
				`Converted date "${trimmed}" to ISO format: "${isoDate}"`,
			);
		}
		return isoDate;
	} catch (error) {
		if (requestId) {
			logWarn(requestId, `Date conversion failed for "${trimmed}": ${error}`);
		}
		return null;
	}
}

/**
 * Supported field value types for CC custom fields
 */
type CCCustomFieldValue = string | number | string[] | number[];

/**
 * Enhanced field name to value mapping with multiple data type support
 */
type CCCustomFieldMap = Record<string, CCCustomFieldValue>;

/**
 * Mapping configuration for AP standard contact fields to CC custom field variations
 *
 * This is the inverse of CC_TO_AP_STANDARD_FIELD_MAPPING found in ccToApCustomFieldsProcessor.ts
 * Maps AP standard field names to arrays of CC custom field name variations to try.
 *
 * When syncing AP standard fields to CC custom fields, we try each variation in the array
 * to find matching CC custom fields using the fieldNamesMatch method.
 */
const AP_TO_CC_STANDARD_FIELD_MAPPING: Record<string, string[]> = {
	// Phone field variations - comprehensive list from CC_TO_AP mapping
	phone: [
		"phone-mobile",
		"phonemobile",
		"phone mobile",
		"telefon mobil",
		"telefon-mobil",
		"telefon",
		"mobile",
		"handy",
		"mobiltelefon",
		"cell phone",
		"cell-phone",
		"cellular",
		"mobile phone",
		"mobile number",
		"mobile-number",
		"cell",
		"cellphone",
		"handynummer",
		"mobilnummer",
		"phone", // Include the standard name too
	],

	// Email field variations
	email: [
		"e-mail",
		"email address",
		"e-mail address",
		"e-mail-adresse",
		"email-adresse",
		"electronic mail",
		"email", // Include the standard name too
	],

	// Name field variations
	firstName: [
		"firstName",
		"first name",
		"first-name",
		"vorname", // German
		"prenom", // French
		"nome", // Italian/Portuguese
	],

	lastName: [
		"lastName",
		"last name",
		"last-name",
		"nachname", // German
		"nom", // French
		"cognome", // Italian
		"sobrenome", // Portuguese
		"surname",
		"family name",
	],

	// Date of birth variations
	dateOfBirth: [
		"dateOfBirth",
		"date of birth",
		"date-of-birth",
		"dob",
		"birth date",
		"birthdate",
		"geburtsdatum", // German
		"date de naissance", // French
		"data di nascita", // Italian
		"fecha de nacimiento", // Spanish
	],

	// Gender variations
	gender: [
		"gender",
		"sex",
		"geschlecht", // German
		"sexe", // French
		"sesso", // Italian
		"sexo", // Spanish/Portuguese
	],

	// Address field variations
	address1: [
		"address1",
		"address",
		"street",
		"street address",
		"adresse", // German/French
		"indirizzo", // Italian
		"direccion", // Spanish
		"endereco", // Portuguese
		"street1",
		"address line 1",
	],

	city: [
		"city",
		"stadt", // German
		"ville", // French
		"citta", // Italian
		"ciudad", // Spanish
		"cidade", // Portuguese
	],

	state: [
		"state",
		"province",
		"region",
		"bundesland", // German
		"provincia", // Italian/Spanish
		"estado", // Spanish/Portuguese
		"etat", // French
	],

	postalCode: [
		"postalCode",
		"postal code",
		"zip",
		"zip code",
		"zipcode",
		"postleitzahl", // German
		"code postal", // French
		"codice postale", // Italian
		"codigo postal", // Spanish/Portuguese
	],

	country: [
		"country",
		"land", // German
		"pays", // French
		"paese", // Italian
		"pais", // Spanish/Portuguese
	],

	// SSN variations
	ssn: [
		"ssn",
		"social security number",
		"social security",
		"social-security-number",
		"tax id",
		"tax-id",
		"national id",
		"national-id",
	],

	// Company/Organization
	companyName: [
		"companyName",
		"company name",
		"company",
		"organization",
		"employer",
		"firma", // German
		"entreprise", // French
		"azienda", // Italian
		"empresa", // Spanish/Portuguese
	],
};

/**
 * Extract AP standard field values and apply mapping to CC custom field variations
 *
 * Takes AP contact data and extracts standard field values (firstName, lastName, email, etc.)
 * For each non-empty standard field, adds all CC custom field name variations to the customFieldMap
 * using the AP_TO_CC_STANDARD_FIELD_MAPPING.
 *
 * @param apContactData - AP contact data containing standard fields
 * @param requestId - Request ID for logging
 * @returns CCCustomFieldMap with standard field mappings
 */
function extractApStandardFieldMappings(
	apContactData: GetAPContactType,
	requestId: string,
): CCCustomFieldMap {
	const customFieldMap: CCCustomFieldMap = {};
	let mappedFieldsCount = 0;
	let totalVariationsCount = 0;

	logDebug(
		requestId,
		"Extracting AP standard field mappings for CC custom fields",
	);

	// Process each AP standard field that has a mapping
	for (const [apFieldName, ccFieldVariations] of Object.entries(
		AP_TO_CC_STANDARD_FIELD_MAPPING,
	)) {
		// Get the value from AP contact data
		const fieldValue = normalizeContactField(
			apContactData[apFieldName as keyof GetAPContactType] as string,
		);

		if (fieldValue) {
			mappedFieldsCount++;

			// Add all CC field variations for this AP standard field
			for (const ccFieldVariation of ccFieldVariations) {
				customFieldMap[ccFieldVariation] = fieldValue;
				totalVariationsCount++;
			}

			logDebug(
				requestId,
				`Mapped AP standard field "${apFieldName}" = "${fieldValue.substring(0, 50)}${fieldValue.length > 50 ? "..." : ""}" to ${ccFieldVariations.length} CC field variations`,
			);
		}
	}

	logInfo(
		requestId,
		`AP standard field mapping completed: ${mappedFieldsCount} fields mapped to ${totalVariationsCount} CC field variations`,
	);

	return customFieldMap;
}

/**
 * Extract AP custom field values and build customFieldMap
 *
 * Fetches AP custom field definitions and extracts custom field values from AP contact data.
 * Builds a customFieldMap using the custom field names as keys and their values.
 *
 * @param apContactData - AP contact data containing customFields array
 * @param requestId - Request ID for logging
 * @returns Promise<CCCustomFieldMap> with custom field mappings
 */
async function extractApCustomFieldMappings(
	apContactData: GetAPContactType,
	requestId: string,
): Promise<CCCustomFieldMap> {
	const customFieldMap: CCCustomFieldMap = {};
	let processedCount = 0;
	let mappedCount = 0;

	logDebug(
		requestId,
		"Extracting AP custom field mappings for CC custom fields",
	);

	// Check if contact has custom fields
	if (!apContactData.customFields || apContactData.customFields.length === 0) {
		logDebug(requestId, "No AP custom fields found in contact data");
		return customFieldMap;
	}

	try {
		// Fetch AP custom field definitions to get field names
		logDebug(
			requestId,
			`Fetching AP custom field definitions for ${apContactData.customFields.length} custom fields`,
		);
		const apCustomFields = await apCustomfield.all();

		if (!apCustomFields || apCustomFields.length === 0) {
			logWarn(requestId, "No AP custom field definitions available");
			return customFieldMap;
		}

		// Create a map of custom field ID to field definition for quick lookup
		const customFieldIdMap = new Map<string, APGetCustomFieldType>();
		for (const field of apCustomFields) {
			customFieldIdMap.set(field.id, field);
		}

		// Process each custom field value from the contact
		for (const customFieldValue of apContactData.customFields) {
			processedCount++;

			// Find the field definition
			const fieldDefinition = customFieldIdMap.get(customFieldValue.id);
			if (!fieldDefinition) {
				logDebug(
					requestId,
					`No field definition found for custom field ID: ${customFieldValue.id}`,
				);
				continue;
			}

			// Normalize and validate the field value
			const normalizedValue = normalizeCustomFieldValue(customFieldValue.value);
			if (!normalizedValue) {
				logDebug(
					requestId,
					`Skipping empty custom field: ${fieldDefinition.name}`,
				);
				continue;
			}

			// Add to custom field map using the field name
			customFieldMap[fieldDefinition.name] = normalizedValue;
			mappedCount++;

			logDebug(
				requestId,
				`Mapped AP custom field "${fieldDefinition.name}" = "${normalizedValue.toString().substring(0, 50)}${normalizedValue.toString().length > 50 ? "..." : ""}"`,
			);
		}

		logInfo(
			requestId,
			`AP custom field mapping completed: ${processedCount} processed, ${mappedCount} mapped`,
		);
	} catch (error) {
		logError(
			requestId,
			"Failed to fetch AP custom fields, proceeding without custom field mappings",
			error,
		);
		// Return empty map - don't fail the entire sync
	}

	return customFieldMap;
}

/**
 * Normalize custom field value from AP contact data
 *
 * @param value - Raw custom field value (string or number)
 * @returns Normalized value or null if empty/invalid
 */
function normalizeCustomFieldValue(
	value: string | number,
): string | number | null {
	if (value === null || value === undefined) {
		return null;
	}

	if (typeof value === "string") {
		const trimmed = value.trim();
		return trimmed === "" ? null : trimmed;
	}

	if (typeof value === "number") {
		return value;
	}

	// For any other type, convert to string and normalize
	const stringValue = String(value).trim();
	return stringValue === "" ? null : stringValue;
}

/**
 * Log comprehensive statistics for AP to CC custom field synchronization
 *
 * Provides detailed breakdown of the synchronization process including:
 * - Standard field mapping statistics
 * - Custom field mapping statistics
 * - Combined field mapping totals
 * - Backward compatibility information
 *
 * @param standardFieldMap - Standard field mappings
 * @param customFieldMap - Custom field mappings
 * @param combinedFieldMap - Combined field mappings
 * @param requestId - Request ID for logging
 */
function logSyncStatistics(
	standardFieldMap: CCCustomFieldMap,
	customFieldMap: CCCustomFieldMap,
	combinedFieldMap: CCCustomFieldMap,
	requestId: string,
): void {
	const standardFieldCount = Object.keys(standardFieldMap).length;
	const customFieldCount = Object.keys(customFieldMap).length;
	const combinedFieldCount = Object.keys(combinedFieldMap).length;
	const overlapCount =
		standardFieldCount + customFieldCount - combinedFieldCount;

	// Log summary statistics
	logInfo(requestId, `=== AP to CC Custom Field Sync Statistics ===`);
	logInfo(
		requestId,
		`Standard field mappings: ${standardFieldCount} (AP standard fields → CC custom field variations)`,
	);
	logInfo(
		requestId,
		`Custom field mappings: ${customFieldCount} (AP custom fields → CC custom fields)`,
	);
	logInfo(
		requestId,
		`Total unique mappings: ${combinedFieldCount} (${overlapCount} overlapping field names resolved)`,
	);

	// Log backward compatibility information
	const hasPhoneMapping = Object.keys(combinedFieldMap).some(
		(key) =>
			key.toLowerCase().includes("phone") ||
			key.toLowerCase().includes("mobile"),
	);
	const hasEmailMapping = Object.keys(combinedFieldMap).some(
		(key) =>
			key.toLowerCase().includes("email") || key.toLowerCase().includes("mail"),
	);

	logInfo(
		requestId,
		`Backward compatibility: Phone mapping=${hasPhoneMapping}, Email mapping=${hasEmailMapping}`,
	);

	// Log field name examples for debugging
	if (standardFieldCount > 0) {
		const standardFieldNames = Object.keys(standardFieldMap).slice(0, 5);
		logDebug(
			requestId,
			`Standard field examples: ${standardFieldNames.join(", ")}${standardFieldCount > 5 ? ` (and ${standardFieldCount - 5} more)` : ""}`,
		);
	}

	if (customFieldCount > 0) {
		const customFieldNames = Object.keys(customFieldMap).slice(0, 5);
		logDebug(
			requestId,
			`Custom field examples: ${customFieldNames.join(", ")}${customFieldCount > 5 ? ` (and ${customFieldCount - 5} more)` : ""}`,
		);
	}

	logInfo(requestId, `=== End Sync Statistics ===`);
}

/**
 * Validate CC patient payload before sending to API
 *
 * Performs validation checks and logs detailed information about the payload
 * to help identify potential issues before making the API call.
 *
 * @param ccPatientData - CC patient payload to validate
 * @param requestId - Request ID for logging
 * @returns Validation result with any issues found
 */
function validateCCPatientPayload(
	ccPatientData: PostCCPatientType,
	requestId: string,
): { isValid: boolean; issues: string[] } {
	const issues: string[] = [];

	logDebug(requestId, "Validating CC patient payload before API call");

	// Check for required fields (at least one identifier)
	if (!ccPatientData.email && !ccPatientData.phoneMobile) {
		issues.push(
			"Missing both email and phoneMobile - at least one is typically required",
		);
	}

	// Check for empty names
	if (!ccPatientData.firstName && !ccPatientData.lastName) {
		issues.push(
			"Missing both firstName and lastName - at least one is typically required",
		);
	}

	// Validate email format if present
	if (ccPatientData.email) {
		const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
		if (!emailRegex.test(ccPatientData.email)) {
			issues.push(`Invalid email format: ${ccPatientData.email}`);
		}
	}

	// Validate phone format if present
	if (ccPatientData.phoneMobile) {
		// Basic phone validation - should contain digits
		const phoneDigits = ccPatientData.phoneMobile.replace(/\D/g, "");
		if (phoneDigits.length < 7) {
			issues.push(
				`Phone number too short: ${ccPatientData.phoneMobile} (${phoneDigits.length} digits)`,
			);
		}
	}

	// Validate date format if present
	if (ccPatientData.dob) {
		const formattedDate = formatDateForCC(ccPatientData.dob, requestId);
		if (!formattedDate) {
			issues.push(
				`Invalid date format for dob: "${ccPatientData.dob}" (expected YYYY-MM-DD or ISO 8601 format)`,
			);
		} else if (formattedDate !== ccPatientData.dob) {
			// Log when date format conversion is needed
			logDebug(
				requestId,
				`Date format conversion needed: "${ccPatientData.dob}" → "${formattedDate}"`,
			);
		}
	}

	// Check for potentially problematic field values
	Object.entries(ccPatientData).forEach(([key, value]) => {
		if (value === "") {
			issues.push(
				`Empty string value for field: ${key} (should be undefined or null)`,
			);
		}
		if (typeof value === "string" && value.length > 255) {
			issues.push(
				`Very long string value for field: ${key} (${value.length} characters)`,
			);
		}
	});

	// Log payload details for debugging
	const payloadSummary = {
		fieldCount: Object.keys(ccPatientData).length,
		hasEmail: !!ccPatientData.email,
		hasPhone: !!ccPatientData.phoneMobile,
		hasFirstName: !!ccPatientData.firstName,
		hasLastName: !!ccPatientData.lastName,
		hasDob: !!ccPatientData.dob,
		hasGender: !!ccPatientData.gender,
		hasSsn: !!ccPatientData.ssn,
	};

	logDebug(
		requestId,
		`CC patient payload summary: ${JSON.stringify(payloadSummary)}`,
	);

	if (issues.length > 0) {
		logWarn(
			requestId,
			`CC patient payload validation issues: ${issues.join("; ")}`,
		);
	} else {
		logDebug(requestId, "CC patient payload validation passed");
	}

	return {
		isValid: issues.length === 0,
		issues,
	};
}

/**
 * Properly typed patient update payload that includes custom fields
 * This extends PostCCPatientType to include the custom field data structure
 * that v3Integration uses (bypassing the number[] constraint)
 */
interface CCPatientUpdatePayload {
	customFields: PostCCPatientCustomfield[];
}

/**
 * Validate and normalize field value based on supported types
 *
 * @param value - Raw field value
 * @param fieldName - Field name for error reporting
 * @returns Normalized value or throws error for unsupported types
 */
function validateAndNormalizeFieldValue(
	value: CCCustomFieldValue,
	fieldName: string,
): CCCustomFieldValue {
	if (value === null || value === undefined) {
		throw new Error(`Field "${fieldName}" has null or undefined value`);
	}

	if (typeof value === "string") {
		const trimmed = value.trim();
		if (trimmed === "") {
			throw new Error(`Field "${fieldName}" has empty string value`);
		}
		return trimmed;
	}

	if (typeof value === "number") {
		if (!Number.isFinite(value)) {
			throw new Error(
				`Field "${fieldName}" has invalid number value: ${value}`,
			);
		}
		return value;
	}

	if (Array.isArray(value)) {
		if (value.length === 0) {
			throw new Error(`Field "${fieldName}" has empty array value`);
		}

		// Check if all elements are strings
		if (value.every((item) => typeof item === "string")) {
			const normalizedArray = (value as string[]).map((item, index) => {
				const trimmed = item.trim();
				if (trimmed === "") {
					throw new Error(
						`Field "${fieldName}" has empty string at index ${index}`,
					);
				}
				return trimmed;
			});
			return normalizedArray;
		}

		// Check if all elements are numbers
		if (value.every((item) => typeof item === "number")) {
			const normalizedArray = (value as number[]).map((item, index) => {
				if (!Number.isFinite(item)) {
					throw new Error(
						`Field "${fieldName}" has invalid number at index ${index}: ${item}`,
					);
				}
				return item;
			});
			return normalizedArray;
		}

		// Mixed types not supported
		throw new Error(
			`Field "${fieldName}" has mixed array element types - only string[] or number[] are supported`,
		);
	}

	throw new Error(
		`Field "${fieldName}" has unsupported value type: ${typeof value}`,
	);
}

/**
 * Standardized field matching using only fieldNamesMatch method
 *
 * Replaces multi-tier matching logic with consistent fieldNamesMatch approach
 * as required for bidirectional custom field type conversion.
 *
 * @param ccCustomFields - Available CC custom fields
 * @param fieldName - Field name to match
 * @param requestId - Request ID for logging
 * @returns Matching CC custom field or undefined
 */
function findMatchingCCCustomField(
	ccCustomFields: GetCCCustomField[],
	fieldName: string,
	requestId: string,
): GetCCCustomField | undefined {
	logDebug(
		requestId,
		`Searching for CC custom field match using fieldNamesMatch for: "${fieldName}"`,
	);

	// Use only fieldNamesMatch for consistent field matching
	// This matches both field name and label using normalized comparison
	const match = ccCustomFields.find(
		(ccf) =>
			fieldNamesMatch(ccf.name, fieldName) ||
			fieldNamesMatch(ccf.label, fieldName),
	);

	if (match) {
		logDebug(
			requestId,
			`Found fieldNamesMatch: "${match.label}" (${match.name})`,
		);
		return match;
	}

	logDebug(requestId, `No CC custom field match found for: "${fieldName}"`);
	return undefined;
}

/**
 * Find matching AP custom field using fieldNamesMatch method
 *
 * Used for type detection and bidirectional conversion support.
 *
 * @param apCustomFields - Available AP custom fields
 * @param fieldName - Field name to match
 * @param requestId - Request ID for logging
 * @returns Matching AP custom field or undefined
 */
function findMatchingAPCustomField(
	apCustomFields: APGetCustomFieldType[],
	fieldName: string,
	requestId: string,
): APGetCustomFieldType | undefined {
	logDebug(
		requestId,
		`Searching for AP custom field match using fieldNamesMatch for: "${fieldName}"`,
	);

	// Use fieldNamesMatch for consistent field matching
	// Check against field name and fieldKey (without contact. prefix)
	const match = apCustomFields.find((apf) => {
		const fieldKey = apf.fieldKey?.replace("contact.", "") || apf.name;
		return (
			fieldNamesMatch(apf.name, fieldName) ||
			fieldNamesMatch(fieldKey, fieldName)
		);
	});

	if (match) {
		logDebug(
			requestId,
			`Found AP fieldNamesMatch: "${match.name}" (ID: ${match.id}, type: ${match.dataType})`,
		);
		return match;
	}

	logDebug(requestId, `No AP custom field match found for: "${fieldName}"`);
	return undefined;
}

/**
 * Create custom field values array handling different data types and allowedValues constraints
 *
 * @param fieldValue - Validated field value
 * @param ccField - Matching CC custom field definition
 * @param fieldName - Field name for logging
 * @param requestId - Request ID for logging
 * @returns Array of custom field value objects
 */
function createCustomFieldValues(
	fieldValue: CCCustomFieldValue,
	ccField: GetCCCustomField,
	fieldName: string,
	requestId: string,
): Array<{ id?: number; value?: string }> {
	const values: Array<{ id?: number; value?: string }> = [];

	// Convert field value to array for uniform processing
	const valueArray = Array.isArray(fieldValue) ? fieldValue : [fieldValue];

	for (const singleValue of valueArray) {
		const stringValue = String(singleValue);

		// Handle allowedValues constraint for dropdown/select fields (v3Integration logic)
		if (ccField.allowedValues && ccField.allowedValues.length > 0) {
			const allowedValue = ccField.allowedValues.find(
				(v) => v.value === stringValue,
			);
			if (allowedValue) {
				// Use ID for predefined values
				values.push({ id: allowedValue.id });
				logDebug(
					requestId,
					`Using predefined value ID ${allowedValue.id} for field "${fieldName}" value "${stringValue}"`,
				);
			} else {
				logInfo(
					requestId,
					`Value "${stringValue}" not found in allowed values for field "${fieldName}", skipping this value`,
				);
				// Skip this value but continue with others
			}
		} else {
			// Free text field - use the string value directly
			values.push({ value: stringValue });
			logDebug(
				requestId,
				`Using free text value "${stringValue}" for field "${fieldName}"`,
			);
		}
	}

	return values;
}

/**
 * Update CliniCore custom fields for a patient with bidirectional type conversion
 *
 * Enhanced version with:
 * - AP to CC field type conversion and value transformation
 * - Standardized field matching using only fieldNamesMatch method
 * - Graceful handling of unmatchable fields without sync failures
 * - Backward compatibility for existing phone/email sync functionality
 *
 * @param patientId - CC patient ID
 * @param fieldNameValueMap - Map of field names to values supporting string, number, string[], number[]
 * @param requestId - Request ID for logging
 * @returns Promise<void>
 */
async function updateCCCustomFields(
	patientId: number,
	fieldNameValueMap: CCCustomFieldMap,
	requestId: string,
): Promise<void> {
	logDebug(
		requestId,
		`Starting CC custom fields update with bidirectional type conversion for patient ${patientId}`,
	);

	if (Object.keys(fieldNameValueMap).length === 0) {
		logDebug(requestId, "No custom field values to sync");
		return;
	}

	try {
		// Step 1: Fetch all available CC custom fields
		logDebug(requestId, "Fetching CC custom field definitions");
		const ccCustomFields = await ccCustomfieldReq.all();

		if (!ccCustomFields || ccCustomFields.length === 0) {
			logInfo(requestId, "No CC custom fields available for matching");
			return;
		}

		// Step 2: Fetch AP custom fields for type detection and conversion
		logDebug(
			requestId,
			"Fetching AP custom field definitions for type conversion",
		);
		let apCustomFields: APGetCustomFieldType[] = [];
		try {
			apCustomFields = await apCustomfield.all();
			logDebug(
				requestId,
				`Fetched ${apCustomFields.length} AP custom fields for type detection`,
			);
		} catch (error) {
			logError(
				requestId,
				"Failed to fetch AP custom fields, proceeding without type conversion",
				error,
			);
			// Continue without AP fields - will use pass-through conversion
		}

		// Step 3: Process and validate field values with type conversion
		const matchedProperties: PostCCPatientCustomfield[] = [];
		let processedCount = 0;
		let convertedCount = 0;
		let unmatchableCount = 0;

		for (const [fieldName, rawFieldValue] of Object.entries(
			fieldNameValueMap,
		)) {
			try {
				processedCount++;

				// Validate and normalize the field value
				const fieldValue = validateAndNormalizeFieldValue(
					rawFieldValue,
					fieldName,
				);

				// Find matching CC custom field using standardized fieldNamesMatch only
				const ccField = findMatchingCCCustomField(
					ccCustomFields,
					fieldName,
					requestId,
				);

				if (!ccField) {
					unmatchableCount++;
					logDebug(
						requestId,
						`No CC custom field found for "${fieldName}" - gracefully skipping (v3Integration pattern)`,
					);
					continue;
				}

				logDebug(
					requestId,
					`Matched field "${fieldName}" to CC custom field "${ccField.label}" (${ccField.name})`,
				);

				// Find matching AP custom field for type conversion
				const apField = findMatchingAPCustomField(
					apCustomFields,
					fieldName,
					requestId,
				);

				let finalFieldValue = fieldValue;

				// Apply type conversion with v3Integration-style permissive approach
				if (apField) {
					// Use more permissive compatibility check (v3Integration approach)
					const isCompatible = areFieldTypesCompatible(apField, ccField, requestId);

					if (isCompatible) {
						// Handle both single values and arrays for multi-value fields
						let originalValue: string | string[];
						if (Array.isArray(fieldValue)) {
							// For arrays, convert each element to string
							originalValue = fieldValue.map((v) => v.toString());
						} else {
							// For single values, convert to string
							originalValue = fieldValue.toString();
						}

						const convertedValue = transformApToCcValue(
							originalValue,
							apField,
							ccField,
							requestId,
						);

						// Update the field value with converted value
						// CC custom fields expect string values, even for multi-value (comma-separated)
						finalFieldValue = convertedValue;

						const originalValueStr = Array.isArray(originalValue)
							? originalValue.join(", ")
							: originalValue;
						if (originalValueStr !== convertedValue) {
							convertedCount++;
							logTypeConversion(
								apField,
								ccField,
								originalValueStr,
								convertedValue,
								requestId,
							);
						}
					} else {
						// v3Integration approach: proceed with sync even if types don't match perfectly
						logInfo(
							requestId,
							`Type mismatch for "${fieldName}" but proceeding with sync (v3Integration compatibility mode)`,
						);
						// Use the field value as-is, converted to string
						finalFieldValue = Array.isArray(fieldValue)
							? fieldValue.map(v => v.toString()).join(", ")
							: fieldValue.toString();
					}
				} else {
					logDebug(
						requestId,
						`No AP field found for "${fieldName}", using pass-through conversion (v3Integration pattern)`,
					);
					// Convert to string format for CC compatibility
					finalFieldValue = Array.isArray(fieldValue)
						? fieldValue.map(v => v.toString()).join(", ")
						: fieldValue.toString();
				}

				// Handle different value types and create appropriate custom field values
				const customFieldValues = createCustomFieldValues(
					finalFieldValue,
					ccField,
					fieldName,
					requestId,
				);

				if (customFieldValues.length === 0) {
					logInfo(
						requestId,
						`No valid values created for field "${fieldName}", skipping`,
					);
					continue;
				}

				// Create custom field value object (v3Integration structure)
				const customFieldValue: PostCCPatientCustomfield = {
					field: ccField,
					values: customFieldValues,
					patient: null,
				};

				matchedProperties.push(customFieldValue);
			} catch (error) {
				logError(requestId, `Error processing field "${fieldName}": ${error}`);
			}
		}

		// Log processing statistics
		logInfo(
			requestId,
			`Field processing completed: ${processedCount} total, ${matchedProperties.length} matched, ${convertedCount} converted, ${unmatchableCount} unmatchable`,
		);

		if (matchedProperties.length === 0) {
			logInfo(
				requestId,
				"No matching CC custom fields found - all fields were unmatchable",
			);
			return;
		}

		// Step 4: Clean and send update request to CC API (v3Integration pattern)
		const payload = cleanData(matchedProperties);
		logInfo(
			requestId,
			`Updating ${matchedProperties.length} custom fields for CC patient ${patientId}`,
		);

		// Create properly typed payload that bypasses the PostCCPatientType constraint
		// This follows the v3Integration pattern where custom fields are sent as objects, not IDs
		const updatePayload: CCPatientUpdatePayload = {
			customFields: payload as PostCCPatientCustomfield[],
		};

		await patientReq.update(patientId, updatePayload as any);

		logInfo(
			requestId,
			`Successfully updated CC custom fields for patient ${patientId} with bidirectional type conversion`,
		);
	} catch (error) {
		logError(requestId, "CC custom fields update error", error);
		throw new Error(`Failed to update CC custom fields: ${error}`);
	}
}

/**
 * Process contact creation and update webhooks from AutoPatient
 *
 * Implements the 4-step logic:
 * 1. Calendar property check (already done in handler)
 * 2. Local database lookup
 * 3. Timestamp comparison if contact found
 * 4. CliniCore synchronization
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param payload - Validated AP contact webhook payload
 * @returns Promise<void> - Completes processing or throws error
 */
export async function contactSyncProcessor(
	requestId: string,
	payload: APContactWebhookPayload,
): Promise<void> {
	const apContactId: string = payload.contact_id as string;
	const email =
		typeof payload.email === "string" ? payload.email.trim() || null : null;
	const phone =
		typeof payload.phone === "string" ? payload.phone.trim() || null : null;

	// Validate that either email or phone is present
	if (!email && !phone) {
		const errorMessage =
			"Processing stopped due to missing required contact data (email or phone)";
		logError(requestId, errorMessage);
		throw new Error(errorMessage);
	}

	logInfo(
		requestId,
		`Processing contact sync for AP ID: ${apContactId}, Email: ${email}, Phone: ${phone}`,
	);

	// Step 2: Local Database Lookup
	const db = getDb();
	let existingPatient: typeof dbSchema.patient.$inferSelect | undefined;

	try {
		// Primary lookup by apId
		const apIdResults = await db
			.select()
			.from(dbSchema.patient)
			.where(eq(dbSchema.patient.apId, apContactId as string))
			.limit(1);

		if (apIdResults.length > 0) {
			existingPatient = apIdResults[0];
			logDebug(
				requestId,
				`Found existing patient by AP ID: ${existingPatient.id}`,
			);
		} else {
			// Secondary lookup by email and phone
			if (email || phone) {
				const conditions = [];
				if (email) conditions.push(eq(dbSchema.patient.email, email));
				if (phone) conditions.push(eq(dbSchema.patient.phone, phone));

				const emailPhoneResults = await db
					.select()
					.from(dbSchema.patient)
					.where(or(...conditions))
					.limit(1);

				if (emailPhoneResults.length > 0) {
					existingPatient = emailPhoneResults[0];
					logDebug(
						requestId,
						`Found existing patient by email/phone: ${existingPatient.id}`,
					);
				}
			}
		}
	} catch (error) {
		logError(requestId, "Database lookup error", error);
		throw new Error(`Failed to lookup existing patient: ${error}`);
	}

	// Step 3A: If Contact Found in Local Database
	if (existingPatient) {
		logInfo(requestId, `Existing patient found: ${existingPatient.id}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId as string);
			logDebug(requestId, `Fetched AP contact data for: ${apContactId}`);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Compare webhook date_updated with local database apUpdatedAt timestamp
		// Use date_updated from webhook payload if available, otherwise use date_created
		const webhookUpdatedDate =
			typeof apContactData.dateUpdated === "string"
				? new Date(apContactData.dateUpdated)
				: new Date(apContactData.dateAdded as string);
		const localUpdatedDate = existingPatient.apUpdatedAt;

		if (localUpdatedDate) {
			const timeDiffMs = Math.abs(
				webhookUpdatedDate.getTime() - localUpdatedDate.getTime(),
			);
			const timeDiffMinutes = timeDiffMs / (1000 * 60);
			const syncBufferMinutes = (getConfig("syncBufferTimeSec") as number) / 60;

			// If timestamps are within the configured sync buffer time: Skip sync
			if (timeDiffMinutes <= syncBufferMinutes) {
				logInfo(
					requestId,
					`Recent update detected (${timeDiffMinutes.toFixed(2)} minutes, buffer: ${syncBufferMinutes} minutes) - skipping sync`,
				);
				return;
			}

			logInfo(
				requestId,
				`Timestamp difference: ${timeDiffMinutes.toFixed(2)} minutes (buffer: ${syncBufferMinutes} minutes) - proceeding with sync`,
			);
		} else {
			logInfo(requestId, "No local timestamp found - proceeding with sync");
		}

		// Continue to CliniCore sync with existing CC ID information
		await syncWithCliniCore(
			requestId,
			existingPatient.id,
			apContactData,
			true,
			existingPatient.ccId,
		);
	} else {
		// Step 3B: If Contact NOT Found in Local Database
		logInfo(requestId, `New contact detected: ${apContactId}`);

		// Fetch complete contact details from AutoPatient API
		let apContactData: GetAPContactType;
		try {
			apContactData = await contactReq.get(apContactId as string);
			logDebug(
				requestId,
				`Fetched AP contact data for new contact: ${apContactId}`,
			);
		} catch (error) {
			logError(requestId, "AutoPatient API fetch error", error);
			throw new Error(`Failed to fetch AP contact data: ${error}`);
		}

		// Add to local database
		let localPatientId: string;
		try {
			const now = new Date();
			const newPatientResults = await db
				.insert(dbSchema.patient)
				.values({
					apId: apContactId as string,
					email: email,
					phone: phone,
					apData: apContactData,
					apUpdatedAt: now,
				})
				.returning({ id: dbSchema.patient.id });

			localPatientId = newPatientResults[0].id;
			logInfo(requestId, `Created new patient record: ${localPatientId}`);
		} catch (error) {
			logError(requestId, "Database insert error", error);
			throw new Error(`Failed to create local patient record: ${error}`);
		}

		// Proceed to CliniCore synchronization (no existing CC ID for new patients)
		await syncWithCliniCore(
			requestId,
			localPatientId,
			apContactData,
			false,
			null,
		);
	}

	logProcessingStep(
		requestId,
		`Contact sync completed for AP ID: ${apContactId}`,
	);
}

/**
 * Sync patient data with CliniCore platform
 *
 * Step 4: CliniCore Synchronization
 * - Use existing CC ID if available, otherwise search CliniCore for existing patient
 * - If patient found: Update existing patient data
 * - If patient NOT found: Create new patient record
 *
 * @param requestId - Request ID for logging
 * @param localPatientId - Local database patient ID
 * @param apContactData - AP contact data
 * @param isUpdate - Whether this is an update (true) or new contact (false)
 * @param existingCcId - Existing CC patient ID if known (prevents duplicate creation)
 * @returns Promise<void>
 */
async function syncWithCliniCore(
	requestId: string,
	localPatientId: string,
	apContactData: GetAPContactType,
	_isUpdate: boolean, // Prefixed with underscore to indicate intentionally unused
	existingCcId?: number | null,
): Promise<void> {
	const db = getDb();
	const now = new Date();

	// Use AP contact data as authoritative source for email/phone (more complete than webhook payload)
	const email = normalizeContactField(apContactData.email);
	const phone = normalizeContactField(apContactData.phone);

	logInfo(
		requestId,
		`Starting CliniCore sync for local patient: ${localPatientId}`,
	);

	// Search CliniCore for existing patient - use existing CC ID if available
	let existingCCPatient: GetCCPatientType | null = null;
	try {
		if (existingCcId) {
			// Use existing CC ID to fetch patient directly
			logDebug(requestId, `Using existing CC ID: ${existingCcId}`);
			try {
				existingCCPatient = await patientReq.get(existingCcId);
				logDebug(
					requestId,
					`Found existing CC patient by ID: ${existingCCPatient.id}`,
				);
			} catch (error) {
				logWarn(
					requestId,
					`Failed to fetch CC patient by existing ID ${existingCcId}, falling back to search`,
					error,
				);
				// Fall through to search by email/phone
			}
		}

		// If no existing CC ID or fetch failed, search by email/phone
		if (!existingCCPatient) {
			if (email) {
				existingCCPatient = await patientReq.search(email);
				if (existingCCPatient) {
					logDebug(
						requestId,
						`Found existing CC patient by email: ${existingCCPatient.id}`,
					);
				}
			}

			if (!existingCCPatient && phone) {
				existingCCPatient = await patientReq.search(phone);
				if (existingCCPatient) {
					logDebug(
						requestId,
						`Found existing CC patient by phone: ${existingCCPatient.id}`,
					);
				}
			}
		}
	} catch (error) {
		logError(requestId, "CliniCore search error", error);
		// Don't throw here - we can still create a new patient
		logInfo(requestId, "Proceeding with patient creation due to search error");
	}

	// Convert AP contact data to CC patient format following v3Integration patterns
	// Keep payload simple and avoid problematic fields like addresses
	const ccPatientData: PostCCPatientType = {
		firstName: normalizeContactField(apContactData.firstName) || undefined,
		lastName: normalizeContactField(apContactData.lastName) || undefined,
		email: email || undefined,
		phoneMobile: phone || undefined, // Map phone to phoneMobile as per v3Integration pattern
		dob:
			formatDateForCC(
				normalizeContactField(apContactData.dateOfBirth),
				requestId,
			) || undefined,
		gender: normalizeContactField(apContactData.gender) || undefined,
		ssn: normalizeContactField(apContactData.ssn) || undefined,
	};

	// Validate payload before sending to CC API
	const validation = validateCCPatientPayload(ccPatientData, requestId);
	if (!validation.isValid) {
		logWarn(
			requestId,
			`CC patient payload has validation issues but proceeding: ${validation.issues.join("; ")}`,
		);
	}

	let finalCCPatient: GetCCPatientType;
	try {
		if (existingCCPatient) {
			// Update existing patient data
			logInfo(
				requestId,
				`Updating existing CC patient: ${existingCCPatient.id}`,
			);

			// Log the payload being sent for debugging
			logDebug(
				requestId,
				`CC patient update payload: ${JSON.stringify(ccPatientData, null, 2)}`,
			);

			finalCCPatient = await patientReq.update(
				existingCCPatient.id,
				ccPatientData,
			);
		} else {
			// Create new patient record
			logInfo(requestId, "Creating new CC patient");

			// Log the payload being sent for debugging
			logDebug(
				requestId,
				`CC patient create payload: ${JSON.stringify(ccPatientData, null, 2)}`,
			);

			finalCCPatient = await patientReq.create(ccPatientData);
		}

		logInfo(requestId, `CC sync completed. CC ID: ${finalCCPatient.id}`);

		// ENHANCED: Comprehensive AP to CC custom fields synchronization
		// Syncs ALL AP standard fields AND custom fields to CC custom fields
		// This replaces the limited phone/email sync with comprehensive mapping
		try {
			logInfo(
				requestId,
				"Starting comprehensive AP to CC custom fields synchronization",
			);

			// Step 1: Extract AP standard field mappings using the comprehensive mapping
			const standardFieldMap = extractApStandardFieldMappings(
				apContactData,
				requestId,
			);

			// Step 2: Extract AP custom field mappings
			const customFieldMap = await extractApCustomFieldMappings(
				apContactData,
				requestId,
			);

			// Step 3: Combine standard and custom field mappings
			// Custom fields take precedence over standard fields if there are naming conflicts
			const combinedFieldMap: CCCustomFieldMap = {
				...standardFieldMap,
				...customFieldMap, // Custom fields override standard fields with same names
			};

			// Step 3.5: Log comprehensive statistics for debugging and monitoring
			logSyncStatistics(
				standardFieldMap,
				customFieldMap,
				combinedFieldMap,
				requestId,
			);

			// Step 4: Sync to CC if we have any fields to sync
			if (Object.keys(combinedFieldMap).length > 0) {
				const standardFieldCount = Object.keys(standardFieldMap).length;
				const customFieldCount = Object.keys(customFieldMap).length;
				const totalFieldCount = Object.keys(combinedFieldMap).length;

				logInfo(
					requestId,
					`Syncing comprehensive field mappings: ${standardFieldCount} standard field variations + ${customFieldCount} custom fields = ${totalFieldCount} total field mappings`,
				);

				await updateCCCustomFields(
					finalCCPatient.id,
					combinedFieldMap,
					requestId,
				);

				logInfo(
					requestId,
					"Comprehensive AP to CC custom fields synchronization completed successfully",
				);
			} else {
				logInfo(
					requestId,
					"No AP fields found for CC custom field synchronization",
				);
			}
		} catch (error) {
			logError(requestId, "Comprehensive custom fields sync error", error);
			// Don't fail the entire sync if custom fields fail
			logInfo(
				requestId,
				"Proceeding with patient sync - comprehensive custom fields sync failed but patient data was saved",
			);
		}
	} catch (error) {
		// Enhanced error logging for CC API errors
		if (error instanceof Error && (error as any).details) {
			const errorDetails = (error as any).details;
			logError(requestId, "CliniCore sync error with details", {
				message: error.message,
				details: errorDetails,
			});

			// Log specific details for debugging
			logError(requestId, `CC API Error Details:`, {
				status: errorDetails.status,
				statusText: errorDetails.statusText,
				url: errorDetails.url,
				method: errorDetails.method,
				requestData: errorDetails.requestData,
				responseData: errorDetails.responseData,
				timestamp: errorDetails.timestamp,
			});
		} else {
			logError(requestId, "CliniCore sync error", error);
		}

		throw new Error(
			`Failed to sync with CliniCore: ${error instanceof Error ? error.message : String(error)}`,
		);
	}

	// Update local database with CC sync results
	try {
		await db
			.update(dbSchema.patient)
			.set({
				ccId: finalCCPatient.id,
				ccData: finalCCPatient,
				ccUpdatedAt: now,
				updatedAt: now,
			})
			.where(eq(dbSchema.patient.id, localPatientId));

		logDebug(
			requestId,
			`Updated local patient record with CC data: ${localPatientId}`,
		);
	} catch (error) {
		logError(requestId, "Database update error", error);
		throw new Error(`Failed to update local patient record: ${error}`);
	}
}

/**
 * Validate AP contact webhook payload
 *
 * @param payload - Raw webhook payload
 * @returns Validated payload or throws error
 */
export function validateContactWebhookPayload(
	payload: APWebhookPayload,
): APContactWebhookPayload {
	if (!payload || typeof payload !== "object") {
		throw new Error("Invalid payload: must be an object");
	}

	// Ensure calendar property is not present for contact webhooks
	if (payload.calendar) {
		throw new Error(
			"Calendar property found - this should be handled as appointment webhook",
		);
	}

	if (!payload.contact_id) {
		throw new Error("Missing required field: contact_id");
	}

	if (!payload.date_created) {
		throw new Error("Missing required field: date_created");
	}

	if (!payload.location) {
		throw new Error("Missing required field: location");
	}

	// Return as contact webhook payload (calendar property excluded by type)
	return payload as APContactWebhookPayload;
}
