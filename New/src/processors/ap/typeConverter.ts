/**
 * AP to CC Type Conversion Utilities
 *
 * Handles bidirectional type conversion between AutoPatient (AP) and CliniCore (CC) custom fields.
 * This module provides the inverse conversion logic for the CC to AP mappings found in
 * ccToApCustomFieldsProcessor.ts and customFieldCreator.ts.
 *
 * Key Features:
 * - AP to CC field type mapping (inverse of CC_TO_AP_DATA_TYPE_MAPPING)
 * - Value transformation for boolean fields (AP RADIO Yes/No → CC boolean true/false)
 * - Detection of boolean RADIO fields based on Yes/No options
 * - Graceful handling of unsupported conversions
 */

import type { APGetCustomFieldType, GetCCCustomField } from "@type";
import { logDebug, logInfo, logWarn } from "@/utils/logger";

/**
 * AP to CC field type mapping
 * Enhanced mapping based on v3Integration analysis to ensure comprehensive coverage
 * Inverse of the CC_TO_AP_DATA_TYPE_MAPPING from customFieldCreator.ts
 */
export const AP_TO_CC_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields (most common and reliable)
	TEXT: "text",
	LARGE_TEXT: "textarea",
	STRING: "text", // Additional text variant

	// Numeric fields
	NUMERICAL: "number",
	FLOAT: "decimal",
	MONETORY: "currency",
	CURRENCY: "currency", // Alternative spelling
	MONEY: "currency", // Additional currency variant

	// Contact fields
	PHONE: "phone",
	EMAIL: "email", // Add email field support

	// Boolean fields - RADIO can be boolean if it has Yes/No options
	CHECKBOX: "boolean",
	RADIO: "boolean", // Will be validated by checking options
	BOOLEAN: "boolean", // Direct boolean mapping

	// Selection fields
	SINGLE_OPTIONS: "select",
	MULTIPLE_OPTIONS: "multiselect",
	DROPDOWN: "select", // Alternative dropdown mapping
	SELECT: "select", // Direct select mapping
	MULTISELECT: "multiselect", // Direct multiselect mapping

	// Date/Time fields
	DATE: "date",
	DATETIME: "date", // Map datetime to date for compatibility
	TIME: "text", // Time fields as text (v3Integration pattern)

	// File fields
	FILE_UPLOAD: "file",
	FILE: "file", // Direct file mapping
	UPLOAD: "file", // Alternative upload mapping
	ATTACHMENT: "file", // Alternative attachment mapping

	// Signature
	SIGNATURE: "signature",

	// Additional field types found in v3Integration analysis
	URL: "text", // URLs as text fields
	WEBSITE: "text", // Website fields as text
	ADDRESS: "text", // Address fields as text
	TEXTAREA: "textarea", // Direct textarea mapping
	TEXTBOX: "text", // Textbox as text
	INPUT: "text", // Generic input as text

	// Fallback for unknown types (v3Integration approach)
	DEFAULT: "text", // Default to text for maximum compatibility
};

/**
 * Check if an AP RADIO field represents a boolean field
 * Boolean RADIO fields are identified by having exactly two options: "Yes" and "No"
 *
 * @param apField - AP custom field to check
 * @returns true if the field is a boolean RADIO field
 */
export function isApRadioBooleanField(apField: APGetCustomFieldType): boolean {
	if (apField.dataType !== "RADIO") {
		return false;
	}

	// Check if field has exactly Yes/No options
	const options = apField.textBoxListOptions;
	if (!options || options.length !== 2) {
		return false;
	}

	const labels = options.map((opt) => opt.label.toLowerCase().trim());
	return labels.includes("yes") && labels.includes("no");
}

/**
 * Transform AP field value to CC format based on field types
 *
 * Enhanced with v3Integration-style robustness and edge case handling
 * Key transformation: AP RADIO "Yes"/"No" → CC boolean "true"/"false"
 * Enhanced to handle multi-value fields (arrays) for checkbox and multi-select fields
 * Includes comprehensive edge case handling for real-world data scenarios
 *
 * @param value - AP field value to transform (string or array for multi-value fields)
 * @param apField - AP custom field definition for type information
 * @param ccField - CC custom field definition for validation
 * @param requestId - Request ID for logging
 * @returns Transformed value suitable for CC (comma-separated for multi-value)
 */
export function transformApToCcValue(
	value: string | string[],
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): string {
	const apDataType = apField.dataType;
	const ccFieldType = ccField.type.toLowerCase().trim();

	// Handle null, undefined, or empty values (v3Integration robustness)
	if (value === null || value === undefined) {
		logDebug(requestId, `Null/undefined value for field "${apField.name}", returning empty string`);
		return "";
	}

	// Handle array values for multi-value fields
	if (Array.isArray(value)) {
		// Filter out null/undefined/empty values from array (v3Integration pattern)
		const validValues = value.filter(v => v !== null && v !== undefined && v !== "");

		if (validValues.length === 0) {
			logDebug(requestId, `Empty array after filtering for field "${apField.name}", returning empty string`);
			return "";
		}

		logDebug(
			requestId,
			`Transforming multi-value array [${validValues.join(", ")}] from AP ${apDataType} to CC ${ccFieldType}`,
		);

		// For multi-value fields, transform each value and join with comma
		const transformedValues = validValues.map((singleValue) =>
			transformApToCcValue(singleValue, apField, ccField, requestId),
		);
		return transformedValues.join(", ");
	}

	// Convert to string and handle edge cases (v3Integration robustness)
	const stringValue = String(value).trim();
	if (stringValue === "") {
		logDebug(requestId, `Empty string value for field "${apField.name}", returning empty string`);
		return "";
	}

	logDebug(
		requestId,
		`Transforming single value "${stringValue}" from AP ${apDataType} to CC ${ccFieldType}`,
	);

	// Handle AP RADIO → CC boolean conversion (enhanced v3Integration pattern)
	if (apDataType === "RADIO" && ccFieldType === "boolean") {
		if (isApRadioBooleanField(apField)) {
			const normalizedValue = stringValue.toLowerCase().trim();

			// Enhanced boolean detection (v3Integration robustness)
			if (normalizedValue === "yes" || normalizedValue === "ja" || normalizedValue === "oui" ||
				normalizedValue === "si" || normalizedValue === "true" || normalizedValue === "1") {
				logDebug(requestId, `Converted AP RADIO "${stringValue}" to CC boolean "true"`);
				return "true";
			} else if (normalizedValue === "no" || normalizedValue === "nein" || normalizedValue === "non" || normalizedValue === "false" || normalizedValue === "0") {
				logDebug(requestId, `Converted AP RADIO "${stringValue}" to CC boolean "false"`);
				return "false";
			} else {
				// v3Integration approach: be more permissive with unexpected values
				logInfo(
					requestId,
					`Unexpected RADIO value "${stringValue}" for boolean field, defaulting to "false" (v3Integration compatibility)`,
				);
				return "false";
			}
		} else {
			logDebug(
				requestId,
				`AP RADIO field "${apField.name}" doesn't have Yes/No options, treating as regular select`,
			);
			return stringValue;
		}
	}

	// Handle AP CHECKBOX → CC boolean/checkbox conversion (enhanced)
	if (apDataType === "CHECKBOX") {
		if (ccFieldType === "boolean") {
			// Enhanced checkbox to boolean conversion (v3Integration pattern)
			const normalizedValue = stringValue.toLowerCase().trim();

			// More comprehensive boolean value detection
			if (
				normalizedValue === "true" ||
				normalizedValue === "1" ||
				normalizedValue === "yes" ||
				normalizedValue === "ja" ||
				normalizedValue === "oui" ||
				normalizedValue === "si" ||
				normalizedValue === "checked" ||
				normalizedValue === "on"
			) {
				logDebug(requestId, `Converted AP CHECKBOX "${stringValue}" to CC boolean "true"`);
				return "true";
			} else {
				logDebug(requestId, `Converted AP CHECKBOX "${stringValue}" to CC boolean "false"`);
				return "false";
			}
		} else if (
			ccFieldType === "checkbox" ||
			ccFieldType.includes("multiselect")
		) {
			// Multi-checkbox to CC checkbox/multiselect - pass through value
			return stringValue;
		}
	}

	// Handle numeric conversions (enhanced v3Integration pattern)
	if (
		apDataType === "NUMERICAL" &&
		(ccFieldType === "number" || ccFieldType === "integer")
	) {
		// Enhanced numeric parsing with v3Integration robustness
		// Remove common non-numeric characters that might be present
		const cleanedValue = stringValue.replace(/[^\d.-]/g, "");
		const numValue = parseFloat(cleanedValue);

		if (!isNaN(numValue) && isFinite(numValue)) {
			const result = ccFieldType === "integer"
				? Math.floor(numValue).toString()
				: numValue.toString();
			logDebug(requestId, `Converted AP NUMERICAL "${stringValue}" to CC ${ccFieldType} "${result}"`);
			return result;
		} else {
			// v3Integration approach: if numeric conversion fails, pass through as text
			logInfo(requestId, `Numeric conversion failed for "${stringValue}", passing through as text`);
			return stringValue;
		}
	}

	if (
		apDataType === "FLOAT" &&
		(ccFieldType === "decimal" || ccFieldType === "float")
	) {
		// Enhanced float parsing
		const cleanedValue = stringValue.replace(/[^\d.-]/g, "");
		const numValue = parseFloat(cleanedValue);

		if (!isNaN(numValue) && isFinite(numValue)) {
			logDebug(requestId, `Converted AP FLOAT "${stringValue}" to CC ${ccFieldType} "${numValue}"`);
			return numValue.toString();
		} else {
			logInfo(requestId, `Float conversion failed for "${stringValue}", passing through as text`);
			return stringValue;
		}
	}

	// Handle AP TEXT → CC number conversion (enhanced v3Integration pattern)
	if (apDataType === "TEXT" && ccFieldType === "number") {
		// Enhanced numeric conversion with better cleaning
		const cleanedValue = stringValue.replace(/[^\d.-]/g, "");
		const numValue = parseFloat(cleanedValue);

		if (!isNaN(numValue) && isFinite(numValue)) {
			// Preserve precision for decimal numbers, remove unnecessary trailing zeros
			const formattedValue =
				numValue % 1 === 0
					? numValue.toString()
					: numValue.toFixed(10).replace(/\.?0+$/, "");
			logDebug(
				requestId,
				`Converted numeric value "${stringValue}" to text format "${formattedValue}" for AP TEXT → CC number field`,
			);
			return formattedValue;
		} else {
			// v3Integration approach: if numeric conversion fails, pass through as-is
			logDebug(
				requestId,
				`Non-numeric value "${stringValue}" passed through for AP TEXT → CC number field`,
			);
			return stringValue;
		}
	}

	// Handle AP PHONE → CC text/telephone conversion (v3Integration pattern)
	if (apDataType === "PHONE" && (ccFieldType === "text" || ccFieldType === "telephone")) {
		// Phone numbers should be passed through as-is for maximum compatibility
		logDebug(
			requestId,
			`Phone field conversion: AP PHONE → CC ${ccFieldType}, value: "${stringValue}"`,
		);
		return stringValue;
	}

	// Handle AP EMAIL → CC text conversion
	if (apDataType === "EMAIL" && ccFieldType === "text") {
		logDebug(
			requestId,
			`Email field conversion: AP EMAIL → CC text, value: "${stringValue}"`,
		);
		return stringValue;
	}

	// Handle AP DATE → CC text conversion (v3Integration pattern)
	if (apDataType === "DATE" && ccFieldType === "text") {
		// Enhanced date handling - preserve date format
		logDebug(
			requestId,
			`Date field conversion: AP DATE → CC text, value: "${stringValue}"`,
		);
		return stringValue;
	}

	// Handle AP MONETORY/CURRENCY → CC text/number conversion
	if ((apDataType === "MONETORY" || apDataType === "CURRENCY") &&
		(ccFieldType === "text" || ccFieldType === "number")) {
		// For currency fields, preserve the value format but clean if needed for numbers
		if (ccFieldType === "number") {
			const cleanedValue = stringValue.replace(/[^\d.-]/g, "");
			const numValue = parseFloat(cleanedValue);
			if (!isNaN(numValue) && isFinite(numValue)) {
				logDebug(requestId, `Currency field conversion: AP ${apDataType} → CC number, value: "${numValue}"`);
				return numValue.toString();
			}
		}
		logDebug(
			requestId,
			`Currency field conversion: AP ${apDataType} → CC ${ccFieldType}, value: "${stringValue}"`,
		);
		return stringValue;
	}

	// Handle AP FILE_UPLOAD → CC text conversion (file references as text)
	if (apDataType === "FILE_UPLOAD" && ccFieldType === "text") {
		logDebug(
			requestId,
			`File field conversion: AP FILE_UPLOAD → CC text, value: "${stringValue}"`,
		);
		return stringValue;
	}

	// Handle AP SIGNATURE → CC text conversion
	if (apDataType === "SIGNATURE" && ccFieldType === "text") {
		logDebug(
			requestId,
			`Signature field conversion: AP SIGNATURE → CC text, value: "${stringValue}"`,
		);
		return stringValue;
	}

	// Handle AP selection fields → CC text conversion (v3Integration fallback pattern)
	if ((apDataType === "SINGLE_OPTIONS" || apDataType === "MULTIPLE_OPTIONS") &&
		ccFieldType === "text") {
		logDebug(
			requestId,
			`Selection field conversion: AP ${apDataType} → CC text, value: "${stringValue}"`,
		);
		return stringValue;
	}

	// For most other cases, pass through the value as-is (v3Integration approach)
	// This handles TEXT→text, LARGE_TEXT→textarea, DATE→date, etc.
	logDebug(
		requestId,
		`Pass-through conversion: AP ${apDataType} → CC ${ccFieldType}, value: "${stringValue}"`,
	);
	return stringValue;
}

/**
 * Get the expected CC field type for an AP field type
 * Enhanced with v3Integration-style fallback logic for maximum compatibility
 *
 * @param apDataType - AP field data type
 * @param apField - AP field definition (for boolean RADIO detection)
 * @returns Expected CC field type, defaults to "text" for unknown types (v3Integration pattern)
 */
export function getExpectedCcFieldType(
	apDataType: string,
	apField?: APGetCustomFieldType,
): string {
	// Special handling for RADIO fields
	if (apDataType === "RADIO" && apField && isApRadioBooleanField(apField)) {
		return "boolean";
	}

	// Try direct mapping first
	const mappedType = AP_TO_CC_TYPE_MAPPING[apDataType];
	if (mappedType) {
		return mappedType;
	}

	// Fallback to text for unknown types (v3Integration approach)
	// This ensures maximum compatibility - any unknown AP field type can sync as text
	return AP_TO_CC_TYPE_MAPPING.DEFAULT || "text";
}

/**
 * Acceptable field type mismatches that should be treated as compatible
 * Enhanced based on v3Integration analysis to handle real-world scenarios
 * These are field type combinations that don't match exactly but should still sync
 */
const ACCEPTABLE_TYPE_MISMATCHES: Array<{
	apType: string;
	expectedCcType: string;
	actualCcType: string;
	description: string;
}> = [
	// Phone field variations (common in international setups)
	{
		apType: "PHONE",
		expectedCcType: "phone",
		actualCcType: "telephone",
		description: "Phone field type variation (phone vs telephone)",
	},
	{
		apType: "PHONE",
		expectedCcType: "phone",
		actualCcType: "text",
		description: "Phone stored as text field",
	},

	// Text field variations (very common)
	{
		apType: "TEXT",
		expectedCcType: "text",
		actualCcType: "textarea",
		description: "Text field type variation (text vs textarea)",
	},
	{
		apType: "LARGE_TEXT",
		expectedCcType: "textarea",
		actualCcType: "text",
		description: "Large text stored as regular text field",
	},

	// Numeric field variations
	{
		apType: "TEXT",
		expectedCcType: "text",
		actualCcType: "number",
		description: "Numeric to text conversion (number values stored as text)",
	},
	{
		apType: "NUMERICAL",
		expectedCcType: "number",
		actualCcType: "text",
		description: "Numeric values stored as text",
	},
	{
		apType: "FLOAT",
		expectedCcType: "decimal",
		actualCcType: "number",
		description: "Decimal stored as number field",
	},
	{
		apType: "NUMERICAL",
		expectedCcType: "number",
		actualCcType: "integer",
		description: "Number field type variation (number vs integer)",
	},

	// Boolean field variations
	{
		apType: "CHECKBOX",
		expectedCcType: "boolean",
		actualCcType: "text",
		description: "Boolean stored as text field (Yes/No values)",
	},
	{
		apType: "RADIO",
		expectedCcType: "boolean",
		actualCcType: "select",
		description: "Boolean radio stored as select field",
	},

	// Selection field variations
	{
		apType: "SINGLE_OPTIONS",
		expectedCcType: "select",
		actualCcType: "dropdown",
		description: "Select field type variation (select vs dropdown)",
	},
	{
		apType: "SINGLE_OPTIONS",
		expectedCcType: "select",
		actualCcType: "radio",
		description: "Select stored as radio field",
	},
	{
		apType: "MULTIPLE_OPTIONS",
		expectedCcType: "multiselect",
		actualCcType: "text",
		description: "Multi-select stored as comma-separated text",
	},

	// Date field variations
	{
		apType: "DATE",
		expectedCcType: "date",
		actualCcType: "text",
		description: "Date stored as text field",
	},
	{
		apType: "DATE",
		expectedCcType: "date",
		actualCcType: "datetime",
		description: "Date field type variation (date vs datetime)",
	},

	// Email field variations
	{
		apType: "EMAIL",
		expectedCcType: "email",
		actualCcType: "text",
		description: "Email stored as text field",
	},

	// File field variations
	{
		apType: "FILE_UPLOAD",
		expectedCcType: "file",
		actualCcType: "text",
		description: "File reference stored as text field",
	},

	// Currency field variations
	{
		apType: "MONETORY",
		expectedCcType: "currency",
		actualCcType: "number",
		description: "Currency stored as number field",
	},
	{
		apType: "MONETORY",
		expectedCcType: "currency",
		actualCcType: "text",
		description: "Currency stored as text field",
	},
];

/**
 * Check if AP and CC field types are compatible for conversion
 * Enhanced validation that checks field properties for comprehensive compatibility
 * Now includes support for acceptable type mismatches that should not fail sync
 *
 * @param apField - AP custom field
 * @param ccField - CC custom field
 * @param requestId - Request ID for logging
 * @returns true if types are compatible for conversion
 */
export function areFieldTypesCompatible(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	requestId: string,
): boolean {
	const expectedCcType = getExpectedCcFieldType(apField.dataType, apField);
	const actualCcType = ccField.type.toLowerCase().trim();

	// With enhanced mapping, we should always have an expected type (defaults to "text")
	// This follows v3Integration's approach of maximum compatibility

	// Basic type compatibility check
	const basicTypeCompatible = expectedCcType === actualCcType;

	if (!basicTypeCompatible) {
		// Check if this is an acceptable mismatch before logging as warning
		const acceptableMismatch = ACCEPTABLE_TYPE_MISMATCHES.find(
			(mismatch) =>
				mismatch.apType === apField.dataType &&
				mismatch.expectedCcType === expectedCcType &&
				mismatch.actualCcType === actualCcType,
		);

		if (acceptableMismatch) {
			logInfo(
				requestId,
				`Acceptable type mismatch handled: AP ${apField.dataType} expects CC ${expectedCcType}, but found CC ${actualCcType} - ${acceptableMismatch.description}`,
			);
			// Continue with compatibility checks for acceptable mismatches
		} else {
			// v3Integration approach: be more permissive with type mismatches
			// Instead of failing, log as info and allow the sync to proceed
			logInfo(
				requestId,
				`Type mismatch (proceeding with v3Integration compatibility): AP ${apField.dataType} expects CC ${expectedCcType}, but found CC ${actualCcType}`,
			);
			// Don't return false here - let the sync proceed with value transformation
		}
	}

	// Enhanced compatibility checks based on field properties

	// 1. Multi-value compatibility check
	const apSupportsMultiValue =
		apField.dataType === "MULTIPLE_OPTIONS" || apField.dataType === "CHECKBOX";
	const ccSupportsMultiValue =
		ccField.allowMultipleValues === true ||
		ccField.type.toLowerCase().includes("multiselect") ||
		ccField.type.toLowerCase().includes("checkbox");

	if (apSupportsMultiValue && !ccSupportsMultiValue) {
		logWarn(
			requestId,
			`Multi-value mismatch: AP field "${apField.name}" supports multiple values but CC field "${ccField.label}" does not`,
		);
		// Allow conversion but log warning - we'll convert to comma-separated string
		logDebug(
			requestId,
			`Proceeding with multi-value to single-value conversion (comma-separated)`,
		);
	}

	// 2. Options compatibility for select/radio fields
	if (
		(apField.dataType === "SINGLE_OPTIONS" ||
			apField.dataType === "MULTIPLE_OPTIONS" ||
			apField.dataType === "RADIO") &&
		(ccField.type.toLowerCase().includes("select") ||
			ccField.type.toLowerCase().includes("radio"))
	) {
		// Check if CC field has allowed values defined
		if (ccField.allowedValues && ccField.allowedValues.length > 0) {
			logDebug(
				requestId,
				`Options-based field compatibility: AP "${apField.name}" → CC "${ccField.label}" (${ccField.allowedValues.length} allowed values)`,
			);
		} else {
			logDebug(
				requestId,
				`Options-based field without predefined values: AP "${apField.name}" → CC "${ccField.label}"`,
			);
		}
	}

	// 3. Boolean field special handling
	if (
		apField.dataType === "RADIO" &&
		isApRadioBooleanField(apField) &&
		ccField.type.toLowerCase() === "boolean"
	) {
		logDebug(
			requestId,
			`Boolean RADIO compatibility confirmed: AP "${apField.name}" (Yes/No) → CC "${ccField.label}" (boolean)`,
		);
	}

	// 4. Numeric field precision compatibility
	if (
		(apField.dataType === "NUMERICAL" || apField.dataType === "FLOAT") &&
		(ccField.type.toLowerCase().includes("number") ||
			ccField.type.toLowerCase().includes("decimal") ||
			ccField.type.toLowerCase().includes("float"))
	) {
		logDebug(
			requestId,
			`Numeric field compatibility: AP ${apField.dataType} → CC ${ccField.type}`,
		);
	}

	logDebug(
		requestId,
		`Enhanced type compatibility confirmed: AP ${apField.dataType} → CC ${actualCcType}`,
	);

	return true;
}

/**
 * Log field type conversion information for debugging
 *
 * @param apField - AP custom field
 * @param ccField - CC custom field
 * @param value - Value being converted
 * @param transformedValue - Value after conversion
 * @param requestId - Request ID for logging
 */
export function logTypeConversion(
	apField: APGetCustomFieldType,
	ccField: GetCCCustomField,
	value: string,
	transformedValue: string,
	requestId: string,
): void {
	const conversionInfo = [
		`Field: "${apField.name}" → "${ccField.label}"`,
		`Type: AP ${apField.dataType} → CC ${ccField.type}`,
		`Value: "${value}" → "${transformedValue}"`,
	].join(", ");

	if (value !== transformedValue) {
		logInfo(requestId, `Type conversion applied: ${conversionInfo}`);
	} else {
		logDebug(requestId, `Type conversion (no change): ${conversionInfo}`);
	}
}
